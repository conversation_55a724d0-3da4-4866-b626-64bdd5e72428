<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">madhat_update_attributes_to_default_form.madhat_update_attributes_to_default_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Update Attributes to Default</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
        <item name="buttons" xsi:type="array">
            <item name="back" xsi:type="string">Magento\Catalog\Block\Adminhtml\Product\Edit\Button\BackButton</item>
            <item name="reset" xsi:type="string">Magento\Catalog\Block\Adminhtml\Product\Edit\Button\ResetButton</item>
            <item name="save" xsi:type="string">MadHat\UpdateAttributesToDefault\Block\Adminhtml\Product\Edit\Button\SaveButton</item>
        </item>
    </argument>
    <settings>
        <namespace>madhat_update_attributes_to_default_form</namespace>
        <ajaxSave>true</ajaxSave>
        <ajaxSaveType>simple</ajaxSaveType>
        <dataScope>data</dataScope>
        <deps>
            <dep>madhat_update_attributes_to_default_form.madhat_update_attributes_to_default_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="madhat_update_attributes_to_default_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="madhat_updateattributestodefault/product_action/savedefault"/>
        </settings>
        <dataProvider class="MadHat\UpdateAttributesToDefault\Ui\Component\Form\DataProvider\Product\AttributesToDefault" name="madhat_update_attributes_to_default_form_data_source">
            <settings>
                <requestFieldName>ids</requestFieldName>
                <primaryFieldName>entity_id</primaryFieldName> <!-- This might not be strictly needed here as we are not editing a single entity -->
            </settings>
        </dataProvider>
    </dataSource>
    <fieldset name="attributes_details">
        <settings>
            <label translate="true">Attributes</label>
            <collapsible>false</collapsible> <!-- Keep it open by default -->
        </settings>
        <!-- Attribute checkboxes will be added here by the DataProvider -->
        <!--
            Example of how a checkbox might look if defined statically:
            <field name="attribute_code_to_reset" formElement="checkbox">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="dataType" xsi:type="string">boolean</item>
                        <item name="label" xsi:type="string" translate="true">Set [Attribute Label] to default</item>
                        <item name="formElement" xsi:type="string">checkbox</item>
                        <item name="prefer" xsi:type="string">toggle</item>
                        <item name="valueMap" xsi:type="array">
                            <item name="true" xsi:type="number">1</item>
                            <item name="false" xsi:type="number">0</item>
                        </item>
                        <item name="default" xsi:type="number">0</item>
                    </item>
                </argument>
            </field>
        -->
    </fieldset>
</form>
